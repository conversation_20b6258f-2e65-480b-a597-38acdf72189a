apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-southeast-2:055313672806:certificate/e54c3523-a26c-4ede-9fa3-9145f28fe0e5
  name: awx-ingress-connect
  namespace: awx
spec:
  ingressClassName: alb
  rules:
    - host: awx.infra.fingermark.tech
      http:
        paths:
          - backend:
              service:
                name: fingermark-awx-service
                port:
                  number: 80
            path: /
            pathType: Prefix
