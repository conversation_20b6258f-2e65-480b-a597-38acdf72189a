apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    # kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: "ip"
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP":80,"HTTPS": 443}]'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:ap-southeast-2:055313672806:certificate/e54c3523-a26c-4ede-9fa3-9145f28fe0e5
    alb.ingress.kubernetes.io/security-groups: "sg-02fea1b93c76be7ac, sg-06f0e2859d7b23d5c" # fm-offices, HavelockNorthAccess
  name: infra-api-ingress-connect
  namespace: infra-api
spec:
  ingressClassName: alb
  rules:
    - host: infra-api.infra.fingermark.tech
      http:
        paths:
          - backend:
              service:
                name: infra-api
                port:
                  number: 8000
            path: /
            pathType: Prefix
